import requests


def send_delete_request(spider_name):
    headers = {
        'Content-type': 'application/json',
        'Accept': 'application/json',
        "token": "FF04CE4B61C0415FB2CBCBFB31500B129F61445C00E345058EF01F503D4AC75C"
    }
    for region in ["eur", "sas"]:
        headers["dbregion"] = region
        url = f"http://data.jobdesk.com/api/DeleteJobs/{spider_name}"
        response = requests.post(url, headers=headers)
        print(f"📤 Sent DELETE request for {spider_name} to {region}: {response.status_code}")

print(send_delete_request("aldi-suisse.ch"))
