import json
import os
import redis

REDIS_HOST = "vultr-prod-2135480d-58c1-41de-9321-ea829c1c337d-vultr-prod-4013.vultrdb.com"
REDIS_PORT = 16752
REDIS_PASSWORD = "qFdO4EkeQEBoWkAHTnqy"
REDIS_USER = "prodscrapy"

r = redis.StrictRedis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    password=REDIS_PASSWORD,
    username=REDIS_USER,
    ssl=True)

os.makedirs('SpiderErrors', exist_ok=True)

try:
    keys = r.keys()
    print("🔑 Redis Keys:", keys)
    jobs_key = "jobs_data"
    # r.delete(jobs_key)
    # key_type = r.type(jobs_key).decode()
    # print(f"🔍 Type of key '{jobs_key}': {key_type}")

    if r.exists(jobs_key):
        jobs_count = r.llen(jobs_key)
        print(f"📊 Total jobs in jobs_data: {jobs_count}")
        jobs_data = r.lrange(jobs_key, 0, -1)
    else:
        print("❌ jobs_data key does not exist in Redis")
    for key in keys:
        key_type = r.type(key)
        if key == b'running_crawlers' and key_type == b'set':
            running_crawlers = r.smembers(key)
            print("🕷️ Running Crawlers:", running_crawlers)
            print("🕷️ Running Crawlers Count:", len(running_crawlers))
        if key == b'spider_errors' and key_type == b'set':
            spider_errors = r.smembers(key)
            spider_errors_list = [e.decode() for e in spider_errors]
            print("🕷️ Spider Errors:", spider_errors)
            print("🕷️ Spider Errors Count:", len(spider_errors))
            with open("SpiderErrors/spider_errors.json", "w") as f:
                json.dump(spider_errors_list, f, indent=2)
except Exception as e:
    print("❌ Connection error:", e)
