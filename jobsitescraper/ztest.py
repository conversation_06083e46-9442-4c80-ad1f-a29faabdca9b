import redis

REDIS_HOST = "vultr-prod-2135480d-58c1-41de-9321-ea829c1c337d-vultr-prod-4013.vultrdb.com"
REDIS_PORT = 16752
REDIS_PASSWORD = "qFdO4EkeQEBoWkAHTnqy"
REDIS_USER = "prodscrapy"

def create_redis_sets():
    client = redis.StrictRedis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        password=REDIS_PASSWORD,
        username=REDIS_USER,
        decode_responses=True,
        ssl=True,
    )

    for key in ["spider_errors", "running_crawlers"]:
        key_type = client.type(key)
        print(f"Key '{key}' type before:", key_type)
        if key_type != 'set':
            print(f"Deleting key '{key}' of wrong type...")
            client.delete(key)
        client.sadd(key, "__init__")
        client.srem(key, "__init__")
        print(f"Key '{key}' created as empty set.")

if __name__ == "__main__":
    create_redis_sets()
