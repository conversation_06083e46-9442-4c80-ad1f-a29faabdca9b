import requests
import json
import random
import requests

class Proxies:
    def get_proxies():
        url2 = "https://proxy.webshare.io/api/proxy/list/"

        header = {"Authorization": "Token a7e428d9571b33d7dbfc4a13eb3204afc4b6fa3e"}
        r = requests.get(url2, headers= header)
        proxy_json = json.loads(r.text)
        proxy_list = proxy_json["results"]
        random.shuffle(proxy_list)

        proxies = []

        for i in range(len(proxy_list)):

            dicx_ =  proxy_list[i]
            username = dicx_["username"]
            password = dicx_["password"]
            proxy_host = dicx_["proxy_address"]
            proxy_port = dicx_["ports"]["http"]

            proxy_auth = username+":"+password
            proxy_ = {
                "https": "https://{}@{}:{}/".format(proxy_auth, proxy_host, proxy_port),
                "http": "http://{}@{}:{}/".format(proxy_auth, proxy_host, proxy_port)
            }
            
            proxies.append(proxy_["http"])
            proxies.append(proxy_["https"])
       
        return proxies
    #get_proxies()
  