import re
from datetime import datetime
from datetime import timedelta
import datedelta

def string_to_date(string_date):
    date=string_date.lower()

    dateString = re.findall(r'\d+', date)
    #print(dateString[0])
    months=["jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"]
    
    if "minuten" in date or "minutes" in date or "menit" in date or "phút" in date:
        mins = int(dateString[0])
        current_time = datetime.now()
        past_time = current_time - timedelta(minutes=mins)
        # print(past_time)

    elif "stunden" in date or "hours" in date or "std." in date or"jam" in date or "giờ" in date or "stunde" in date or "hour" in date:
        
        hours = int(dateString[0])
        current_time = datetime.now()
        past_time = current_time - timedelta(hours=hours)
        # print(past_time)
    
    elif "tagen" in date or "days" in date or "day" in date  or "tg." in date or "ngày " in date or "tag" in date:
        
        days = int(dateString[0])
        current_time = datetime.now()
        past_time=current_time-datedelta.datedelta(days=days)
        # print(past_time)
        
    elif "weeks" in date or "week" in date or "wochen" in date:
        weeks = int(dateString[0])
        current_time = datetime.now()
        past_time = current_time - timedelta(weeks=weeks)
        # print(past_time)
        
    
    elif "months" in date or "tháng" in date or "monaten" in date or "month" in date:
        
        if dateString == []:
            months=1
        else:     
            months = int(dateString[0])
        current_time = datetime.now()
        past_time = current_time - datedelta.datedelta(months=months)
        # print(past_time)
    
    elif "yesterday" in date or "kemarin" in date:
        current_time = datetime.now()
        past_time=current_time-datedelta.datedelta(days=1)
        # print(past_time)
    else:
        past_time=''

    return past_time