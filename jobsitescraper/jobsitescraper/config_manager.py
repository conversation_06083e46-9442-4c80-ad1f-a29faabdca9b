import requests
from zipfile import ZipFile
import os
import json
import shutil
import configparser


class ConfigManager:
    def getConfig(argument=None):

        second_array = []
        try:
            token = {
                "Token": "FF04CE4B61C0415FB2CBCBFB31500B129F61445C00E345058EF01F503D4AC75C"
            }

            stream_url = "http://data.jobdesk.com/api/GetJobSiteCrawlerConfig/"
            # stream_url = "http://*************:7676/api/test"

            def download_file(url):
                headers = {'Content-type': 'application/json',
                                    'Accept': 'application/json',
                                    'token':token['Token']}
                local_filename = "CustomCrawler.zip"
                # NOTE the stream=True parameter below
                with requests.post(url, headers=headers, json=token, stream=True) as r:
                    with open(local_filename, 'wb') as f:
                        for chunk in r.iter_content(chunk_size=8192):
                            # If you have chunk encoded response uncomment if
                            # and set chunk_size parameter to None.
                            # if chunk:
                            f.write(chunk)
                return local_filename

            filename = download_file(stream_url)

            # opening the zip file in READ mode
            with ZipFile(filename, 'r') as zip:
                # printing all the contents of the zip file
                # zip.printdir()

                # extracting all the files
                #print('Extracting all the files now...')
                zip.extractall()
                print('Extracting Zip Done!')

            # Remove all crawlers from spiders Folder
            for root, dirs, files in os.walk('jobsitescraper/spiders'):
                for f in files:
                    os.unlink(os.path.join(root, f))
                for d in dirs:
                    shutil.rmtree(os.path.join(root, d))

            all_crawlers = []
            path_to_crawlers = 'jobsite-scrapy-scripts'

            # Reading all files with Extension .py from api folder
            crawler_files = [pos_crawler for pos_crawler in os.listdir(
                path_to_crawlers) if pos_crawler.endswith('.py')]

            try:
                # Creating list of crawlers
                for file in os.listdir(path_to_crawlers):
                    full_filename = "%s/%s" % (path_to_crawlers, file)
                    with open(full_filename, 'r') as fi:
                        all_crawlers.append(fi)

                # Copying crawlers from API Folder to Spiders
                for crawler in all_crawlers:
                    shutil.copy(crawler.name, 'jobsitescraper/spiders')

            except Exception:
                print("\n###########################\n")
                print("Crawler File  Error")
                print("\n###########################\n")

            all_dicts = []
            path_to_json = 'jobsite-scrapy-config/'
            json_files = [pos_json for pos_json in os.listdir(
                path_to_json) if pos_json.endswith('.json')]
            try:
                for file in os.listdir(path_to_json):
                    full_filename = "%s/%s" % (path_to_json, file)

                    # Open the File and write to it, This will change the data
                    with open(full_filename, 'r') as fi:
                        dict = json.load(fi)

                    dict['RecentJobs'] = argument
                    if argument == False:
                        dict['MaxPagesToCrawl'] = 1000

                    with open(full_filename, 'w') as f:
                        json.dump(dict, f, indent=2)
                        all_dicts.append(dict)
            except Exception:
                print("\n###########################\n")
                print("Config File Structure Error")
                print("\n###########################\n")

            for i in all_dicts:
                if i["IsActive"] is True:
                    second_array.append(i)

            shutil.rmtree("jobsite-scrapy-config/")
            shutil.rmtree("jobsite-scrapy-scripts/")
        except Exception as e:
            print(f"Configs API Error: {e}")
        return second_array