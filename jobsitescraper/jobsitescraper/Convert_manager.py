from jobsitescraper.log_manager import CustomLogger
import re
from bs4 import BeautifulSoup
import json
from datetime import datetime
from datetime import timezone
from urllib.parse import urlparse
from html import unescape  # python 3.4+
from urllib.parse import urljoin
from urllib import parse
import dateutil.parser as parser
import traceback
import sys

class ConvertManager:

    def converter_parse_job(self,config, upld, response):
        pass
    def converter_parse_job(self,config, response):
        try: 
            website_text = response.body.decode("utf-8",errors='ignore')
            jobs_soup = BeautifulSoup(website_text.replace("<", " <"), "html.parser")
            script_tag = jobs_soup.find('script', type='application/ld+json', string=re.compile("JobPosting"))
            
            try:
                if script_tag is not None:
                    if "ictjobs.ch" in config["SourceKey"]:
                        text = script_tag.contents[0].replace("\n", " ").replace("00.00", '"00.00"')
                        data = json.loads(text, strict=False)
                    else:
                        data = json.loads(script_tag.contents[0], strict=False)
                    if '@graph' in data:
                        data = data['@graph'][0]
                    ad = {}
                    ad['JobTitle'] = None
                    ad["SourceURL"] = response.url
                    ad["JsonDataString"] = json.dumps(data,ensure_ascii=False)
                    if type(data)==list:
                        data = data[0]
                    if(type(data['hiringOrganization']) == str):
                        ad['CompanyName'] = data['hiringOrganization']
                    else:
                        if 'name' in data['hiringOrganization']:
                            ad['CompanyName'] = data['hiringOrganization']['name']
                        if 'email' in data['hiringOrganization']:
                            ad['JobContactEmails'] = data['hiringOrganization']['email']
                        if 'telephone' in data['hiringOrganization']:
                            ad['JobContactPhone'] = data['hiringOrganization']['telephone']
                        if 'url' in data['hiringOrganization']:
                            ad['CompanyURL'] = data['hiringOrganization']['url']
                        if 'logo' in data['hiringOrganization']:
                                ad['CompanyLogoFileURL'] = data['hiringOrganization']['logo']    
                    ad['CrawlTimestamp'] = datetime.now(
                        timezone.utc).astimezone().isoformat()
                    if 'employmentType' in data:
                        raw_employment = data['employmentType']
                        employment_type = ''
                        if type(raw_employment) == str:
                            employment_type = ''.join(map(str, raw_employment))
                        else:
                            for i in raw_employment:
                                employment_type += ", "+i
                        ad['JobContractTypeText'] = employment_type.strip(",").strip()
                    if 'jobLocation' in data:
                        ad['JobLocation'], ad ['JobLocationCountry'] =ConvertManager.get_address(data)
                    if 'title' in data:
                        if 'title' in data['title']:
                            ad['JobTitle'] = data['title']['title']
                        else:
                            ad['JobTitle'] = data['title']
                    if 'datePosted' in data:
                        raw_date = data['datePosted']
                        try:
                            date = parser.parse(raw_date)
                            post_date = date.isoformat()
                        except:
                            post_date = ""
                        ad['PostedDate'] = post_date
                    if 'industry' in data:
                        ad['CompanyIndustry'] = data['industry']
                        ad['JobSector'] = data['industry']
                    if 'description' in data: 
                        description = unescape(data['description'])
                        RawContent = BeautifulSoup(description.replace("<"," <"), "html.parser")
                        cleanContent = RawContent.get_text()
                        ad['CleanContent'] = cleanContent
                        ad['RawContent'] = description
                    if 'validThrough' in data:
                        ad['ExpiryDate'] = data['validThrough']
                        if ad['ExpiryDate'] is None:
                            del ad['ExpiryDate'] 
                    ad['SourceKey'] = config["SourceKey"]
                    ad['SourceCountry'] = config["SourceCountry"]
                    ad['SourceLangCode'] = config["LangCode"]
                    # ad ['JobLocationCountry'] = config["SourceCountry"]
                    ad['SourceUID'] = response.url
                    return ad
                else:
                    if 'neuvoo' not in config["SourceKey"] and 'mustakbil' not in config["SourceKey"]:
                        CustomLogger.LogEvent(config["SourceKey"],   "No Google JSON")
                        return None
            except Exception as e:
                CustomLogger.LogEvent(config["SourceKey"], ": "+str(e))
                print(traceback.format_exc())
                sys.stdout.flush()
        except Exception as e:
            CustomLogger.LogEvent(config["SourceKey"], ": "+str(e))
            print(traceback.format_exc())
            sys.stdout.flush()


    def get_address(data):
        address = ""
        job_country = ""
        if 'jobLocation' in data:
            joblocation = data['jobLocation']
            if type(joblocation) == list:
                joblocation = joblocation[0]
            if 'address' in joblocation:
                job_location_address = joblocation['address']
                if type(job_location_address) == list:
                    job_location_address = job_location_address[0]
                if 'addressLocality' in job_location_address:
                    address_locality = job_location_address['addressLocality']
                    if address_locality is not None:
                        address = address_locality
                if 'addressRegion' in job_location_address:
                    address_region = job_location_address['addressRegion']
                    if address_region is not None:
                        address += " "+address_region
                if 'addressCountry' in job_location_address:
                    address_Country = None
                    address_Country = job_location_address['addressCountry']
                    if address_Country is not None:
                        try:
                            address += " "+address_Country
                            job_country = address_Country
                        except:
                            if "name" in address_Country:
                                address_Country = job_location_address['addressCountry']['name']
                                address += " "+ job_location_address['addressCountry']['name']
                                job_country = address_Country

                if 'streetAddress' in job_location_address:
                    address_street = job_location_address['streetAddress']
                    if address_street is not None:
                        address += " "+address_street
        return address, job_country
    
    def text_between(text, start, end):
        result = re.search('%s(.*)%s' % (start, end), text).group(1)
        if result is not None:
            return result
        else: 
            return text
    
    def get_url_param(url,param):
        result=parse.parse_qs(parse.urlparse(url).query)[param][0]
        return result
    
    def get_region(countryCode):
        eur_list = [ "al", "ad", "am", "at", "ba", "be", "bg", "by", "ch","fo", "ge", "gi", "hr", "cy", "cz", "dk", "ee", "fl", "fr", "de", "gr", "hu", "ie", "im", "is", "it", "li", "lv", "lt", "lu", "mc", "me", "mt", "mk", "nl","no", "pl", "pt", "ro","ru","rs","sm", "sk", "sl", "es", "se", "xk" ,"tr","ua","gb","va"]
        region= "eur"
        
        if countryCode not in eur_list:
            region = "sas"
        
        return region