import os
import re
import redis
from bs4 import BeautifulSoup, Comment
from jobsitescraper.Convert_manager import ConvertManager
from jobsitescraper.utils import env
from itemadapter import ItemAdapter
import requests
import traceback
import json
import re
import sys
from dateutil import parser

class jobsitescraperPipeline:
    def __init__(self):
        self.redis_host = env("REDIS_HOST")
        self.redis_port = env("REDIS_PORT")
        self.redis_password = env("REDIS_PASSWORD")
        self.redis_user = env("REDIS_USER")
        self.redis_client = redis.StrictRedis(
            host=self.redis_host,
            port=self.redis_port,
            username=self.redis_user,
            password=self.redis_password,
            decode_responses=True,
            ssl=True)


    def process_item(self, item, spider):
        try:
            self.spider = spider

            if "RawContent" in item and item["RawContent"]:
                item["RawContent"] = self.clean_rawcontent(item["RawContent"])

            api_url = env("AI_API")
            api_res = requests.post(api_url, json=item, verify=False)
            api_res.raise_for_status()
            res = api_res.json()
            for key in res:
                item[key] = res[key]

            if "CompanyName" not in item  or len(item["CompanyName"]) == 0 or item["CompanyName"] == self.spider.config["SourceKey"]:
                if "CompanyName" not in self.spider.config:
                    print(f"[ {self.spider.name} ] Company Name not found in config")
                    print(self.spider.config)
                else:
                    item["CompanyName"] = self.spider.config["CompanyName"]

            if "IsRecruiterCompany" in self.spider.config and self.spider.config["IsRecruiterCompany"] == True:
                item["IsRecruiterCompany"]= True
            try:
                date_time = parser.parse(item["PostedDate"])
                item["PostedDate"]= date_time.strftime("%m/%d/%Y %H:%M:%S")
            except:
                print(f"[ {self.spider.name} ] Invalid Posted date")

            if self.is_valid_ad(item):
                redis_res = self.redis_client.rpush("jobs_data", json.dumps(item))
                print(f"[ {self.spider.name} ] Jobs : {self.spider.count} Total Jobs in redis : {redis_res}")
            else:
                print(f"[ {self.spider.name} ] Invalid Job ")
        except:
            print(f"[ {self.spider.name} ] Exception ")
            print(traceback.format_exc())
            sys.stdout.flush()
        return item

    def is_valid_ad(self, item):
        if "JobLocationText" not in item or len(item["JobLocationText"]) == 0:
            print(f"[ {self.spider.name} ] Job Location Text NULL ")
            return False
        elif "JobLocationCountry" not in item or len(item["JobLocationCountry"])== 0:
            print(f"[ {self.spider.name} ] Job Location Country NULL ")
        #     return False
        if "JobTitle" not in item  or len(item["JobTitle"]) == 0:
            print(f"[ {self.spider.name} ] Job Title NULL ")
            return False
        if "CompanyName" not in item  or len(item["CompanyName"]) == 0:
            print(f"[ {self.spider.name} ] CompanyName NULL ")
            return False
        if "CompanyLogoFileURL" not in item or len(item['CompanyLogoFileURL']) == 0:
            print(f"[ {self.spider.name} ] CompanyLogoFileURL NULL ")
            return False
        return True

    def get_matching_tag(self,sourceUid, title, cleanContent, api_url):
        matching_tags = ""
        try:
            body={"JobTitle":title, "JobContent":cleanContent, "SourceUID":sourceUid}
            api_res = requests.post(api_url, json=body)
            res = api_res.json()
            if "TagId" in res:
                matching_tags = ','.join([str(i) for i in res["TagId"]])
        except Exception as e:
            print(traceback.format_exc())
        return matching_tags

    def clean_rawcontent(self, rawcontent):
        website_text = rawcontent.replace("<", " <")
        jobs_soup = BeautifulSoup(website_text, "html.parser")
        for tag in jobs_soup.find_all(['img', 'a', 'script', 'svg', 'path', 'circle', 'source', 'button', 'footer',
                                       'picture', 'style', 'iframe', 'noscript', 'meta', 'link', 'source']):
            tag.decompose()
        for comment in jobs_soup.find_all(text=lambda text: isinstance(text, Comment)):
            comment.extract()
        return jobs_soup.decode_contents()

class jobsitescraperAssignImagePipeline:
    def __init__(self):
        pass
    def process_item(self, item, spider):
        if "CompanyLogoFileURL" in item and len(item["CompanyLogoFileURL"]) > 0:
            item["image_urls"] = [item["CompanyLogoFileURL"]]
        return item

class jobsitescraperUploadImagePipeline:
    def __init__(self):
        pass
    def process_item(self, item, spider):
        if "images" in item and len(item["images"]) > 0:
            try:
                _img = item["images"][0]
                _token = {
                    "Token": env("TOKEN")
                }
                _headers = {'Token': _token['Token']}
                _url = "https://data.jobdesk.com/admin/UploadToPublicCache"
                _region = ConvertManager.get_region(item["SourceCountry"])
                _normalized_company_name = re.sub(r'[-()\"#/@;:<>{}`+=~|.!?, ]', "", item['CompanyName'].upper())
                _directoryKey = f"companies/{item['SourceCountry']}/{_normalized_company_name}"
                _payload = {'RegionCode': _region,
                    'DirectoryKey': _directoryKey, 'ApplicationKey': 'd'}
                _files = {"companylogo.jpg": open(f"./temp_images/{_img['path']}", 'rb')}

                response = requests.post(
                    _url, files=_files, data=_payload, headers=_headers).json()
                if(response["status"]=="ok" and  len(response["filesAdded"])>0):
                    item["CompanyLogoFileURL"]= response["filesAdded"][0]
            except Exception as e:
                print(traceback.format_exc())
        return item

class jobsitescraperDeleteImagePipeline:
    def __init__(self):
        pass
    def process_item(self, item, spider):
        if "images" in item and len(item["images"]) > 0:
            _img = item["images"][0]
            _img_path = f"./temp_images/{_img['path']}"
            if os.path.exists(_img_path):
                    os.remove(_img_path)
            else:
                    print("The file does not exist")
        return item