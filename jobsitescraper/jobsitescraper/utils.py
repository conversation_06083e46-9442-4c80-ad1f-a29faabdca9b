import os
import dotenv
from datetime import datetime


def env(name, file: str = None):
    """
    To reduce verbosity when calling environment variables
    :param name: Name of KEY of the environment variable
    :param file: Optional file name for a custom .env file
    :return: The value of the environment variable
    """
    env_file = file if file else dotenv.find_dotenv()

    if env_file:
        dotenv.load_dotenv(env_file)
        print(f"Dotenv file loaded from: {env_file}")
    else:
        print("No .env file found.")

    env_var = os.getenv(name)

    if not env_var:
        print(f"Key '{name}' not found in the .env file.")

    return env_var


def age_calculator(birthyear):
    birthyear = int(birthyear)
    currentYear = datetime.now().year
    age = currentYear - birthyear
    return age


a = env('TAKEOFF_CONFIG_API')
print("TAKEOFF_CONFIG_API:", a)

