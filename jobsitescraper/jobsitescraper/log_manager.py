from datetime import datetime
from datetime import timezone
import requests
import sys
from jobsitescraper.utils import env


class CustomLogger:
    def LogEvent(source: str, msg: str):
        date = datetime.now(timezone.utc).astimezone().isoformat()
        log_dict = {}
        log_dict["Source"] = source
        log_dict["Message"] = msg
        log_dict["Timestamp"] = datetime.now(timezone.utc).astimezone().isoformat()

        # log_url = env("LOG_API")
        # headers = {'Content-type': 'application/json',
        #                         'Accept': 'application/json',
        #                         'token':env("TOKEN")}
        # log_req = requests.post(log_url, headers=headers,json=log_dict)
        # print("[",str(source),"] " ,msg)
        # sys.stdout.flush()
        
    def LogMessage(source,msg):
        print(str(source) ,msg)
        sys.stdout.flush()
        
        