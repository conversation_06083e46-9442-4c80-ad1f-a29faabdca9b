from bs4 import BeautifulSoup
from jobsitescraper.log_manager import CustomLogger
import scrapy
import re
from datetime import datetime
from datetime import timezone
import traceback
import sys
from jobsitescraper.utils import env
from scrapy.exceptions import CloseSpider


class allreal_CrawlingManager(scrapy.Spider):
    name = "4plcs.com"
    close_down = False
    config = {}
    isLive = env("PRODUCTION")
    count = 0

    def __init__(self, _config=None, *args, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == "True":
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        config = {}
        config["SourceKey"] = "4plcs.com"
        config["BaseUrl"] = "https://4plcs.com/"
        config["StartUrl"] = "https://4plcs.com/careers/"
        config["SourceCountry"] = "ch"
        config["LangCode"] = "de"
        config['CompanyName'] = "4plcs"
        config["Upload"] = True
        config["IsActive"] = True
        config["Custom"] = True
        config['MaxPagesToCrawl'] = 10
        config['MaxJobsToCrawl'] = 500
        return config

    # Starting request
    def start_requests(self):
        try:
            if self.config is None:
                CustomLogger.LogEvent(self.name, "No Config Read")
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], " Crawler Started")
                yield scrapy.Request(self.config["StartUrl"], method='GET', callback=self.extract_links)
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def extract_links(self, response):
        try:
            links = response.xpath('//div[@class="blog-list-wrapper w-dyn-list"]')
            for i in links:
                link = i.xpath(".//div/div/a/@href").get()
                title = i.xpath('.//div/div/a/div/div[2]/text()').get()
                jobtype = i.xpath('.//div/div/a/div[3]/div/text()').get()
                href = response.urljoin(link)
                meta = {'title': title, 'JobType': jobtype}
                yield response.follow(href, callback=self.parse_job, meta=meta)

        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_job(self, response):
        if self.close_down == True:
            raise CloseSpider("took down by analyzer")
        try:
            jobTitle = response.request.meta["title"]
            if jobTitle is not None:
                jobTitle = jobTitle.strip()
            else:
                jobTitle = ''

            jobtype = response.request.meta["JobType"]
            if jobtype is not None:
                jobtype = jobtype
            else:
                jobtype = ''

            website_text = response.body.decode("utf-8")
            jobs_soup = BeautifulSoup(website_text.replace("<", " <"), "html.parser")
            description = jobs_soup.find('div', {"class": "container"})
            if description is not None:
                cleanContent = re.sub('\s+', ' ', description.get_text())
                rawContent = re.sub('\s+', ' ', description.decode_contents())
            else:
                cleanContent = ''
                rawContent = ''

            ad = {}
            ad['JobTitle'] = jobTitle
            ad['JobLocation'] = 'Rittergasse 35, 4051 Basel, Switzerland'
            ad['CompanyName'] = self.config['CompanyName']
            ad['JobTypeText'] = jobtype
            ad["SourceURL"] = response.url
            ad['SourceCountry'] = self.config["SourceCountry"]
            ad['SourceKey'] = self.config["SourceKey"]
            ad['SourceLangCode'] = self.config["LangCode"]
            ad['CrawlTimestamp'] = datetime.now(timezone.utc).astimezone().isoformat()
            ad['SourceUID'] = response.url
            ad['CleanContent'] = cleanContent
            ad['RawContent'] = rawContent
            ad['PostedDate'] = datetime.now(timezone.utc).astimezone().isoformat()

            emailList = re.findall(
                '\S+@\S+', cleanContent.strip("\n"))
            phoneList = re.findall(r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]',
                                   cleanContent.strip("\n").replace('\u00a0', ' '))
            if len(emailList) > 0:
                _email = emailList[0]
                ad['JobContactEmails'] = _email
            if len(phoneList) > 0:
                for i in range(len(phoneList)):
                    phone = phoneList[i].strip().strip('(').strip(')')
                    if len(phone) > 0:
                        ad['JobContactPhone'] = phone

            if self.config["Upload"] is True:
                self.count += 1
                if self.count >= self.config["MaxJobsToCrawl"]:
                    self.crawler.engine.close_spider(self, 'Scraped jobs.')
                else:
                    yield ad
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], "Scraped But not uploaded")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def close(self, reason):
        try:
            CustomLogger.LogEvent(self.config["SourceKey"], f"Crawler Stopped, Total Jobs: {self.count}")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
