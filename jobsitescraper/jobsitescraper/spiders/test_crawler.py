from scrapy_playwright.page import PageMethod
from jobsitescraper.log_manager import CustomLogger
from jobsitescraper.utils import env
from bs4 import BeautifulSoup
from datetime import datetime, timezone
import sys, traceback
import scrapy, re
from scrapy.exceptions import CloseSpider


class JobsCHRtCrawlingManager(scrapy.Spider):
    name = "jobs.ch_all"
    close_down = False
    count = 0
    page_num = 1
    isLive = env("PRODUCTION")

    def __init__(self, _config=None, *args, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == "True":
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        config = {}
        config["SourceKey"] = "jobs.ch"
        config["BaseUrl"] = "https://www.jobs.ch"
        config["StartUrl"] = ['https://www.jobs.ch/en/companies/?page=[page_num]&region=1&term=',
                              'https://www.jobs.ch/en/companies/?page=[page_num]&region=2&term=',
                              'https://www.jobs.ch/en/companies/?page=[page_num]&region=3&term=',
                              'https://www.jobs.ch/en/companies/?page=[page_num]&region=4&term='
                              ]
        config["SourceCountry"] = "ch"
        config["LangCode"] = "de"
        config['CompanyName'] = 'jobs.ch'
        config["Upload"] = True
        config["IsActive"] = True
        config["Custom"] = True
        config["Depth"] = 1
        config["MaxPagesToCrawl"] = 100
        config['MaxJobsToCrawl'] = 500000
        config["RecentJobs"] = True
        config['DeleteAllJobsOnStart'] = False
        return config

    def start_requests(self):
        try:
            if self.config is None:
                CustomLogger.LogEvent(self.name, "No Config Read")
            else:
                if self.config["RecentJobs"]:
                    CustomLogger.LogEvent(self.config["SourceKey"], "Recent Data thread started")
                    for start_url in self.config["StartUrl"]:
                        url = start_url.replace("[page_num]", str(self.page_num))
                        yield scrapy.Request(url, method='GET', callback=self.parse_all)
                else:
                    print("No Need to run")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_all(self, response):
        try:
            # Decode response body with UTF-8 if needed
            if hasattr(response, 'body'):
                website_text = response.body.decode('utf-8', errors='ignore')
            
            links = response.xpath('//div[@data-cy="company-search-serp"]')
            for i in links:
                link = i.xpath('.//div/div/div/a/@href').get()
                jobs = i.xpath('.//div/div/div[2]/div/a/span/text()').get()
                company = i.xpath('.//div/div/div/a/h2/text()').get()
                href = self.config["BaseUrl"] + link
                meta = {'jobs': jobs, 'company': company}
                yield response.follow(href, callback=self.parse_company, meta=meta)
            
            current_url = response.url
            self.page_num += 1
            if self.page_num <= self.config["MaxPagesToCrawl"]:
                next_page = re.sub(r'page=\d+', f'page={self.page_num}', current_url)
                yield scrapy.Request(url=next_page, method='GET', callback=self.parse_all)
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_company(self, response):
        try:
            # Decode response body with UTF-8 if needed
            if hasattr(response, 'body'):
                website_text = response.body.decode('utf-8', errors='ignore')
            
            jobs = response.meta['jobs']
            company = response.meta['company']
            if jobs is not None:
                jobs = jobs.replace(' jobs', '').replace(' job', '')
            else:
                jobs = 0
            if company is not None:
                company = company
            else:
                company = ''
            c_link = response.xpath('//div[@class="d_inline-grid ai_center gap_s8 grid-tc_[1fr_auto]"]/parent::a/@href').get()
            yield {"CompanyName": company, "TotalJobs": jobs, "CompanyURL": c_link}
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()
