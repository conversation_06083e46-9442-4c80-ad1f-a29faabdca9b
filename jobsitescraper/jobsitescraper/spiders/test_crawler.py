from jobsitescraper.log_manager import CustomLogger
from jobsitescraper.utils import env
from scrapy.exceptions import CloseSpider
from bs4 import BeautifulSoup
from datetime import datetime
from datetime import timezone
import sys, traceback
import scrapy
import re
from scrapy_playwright.page import PageMethod


class sabag_ch_CrawlingManager(scrapy.Spider):
    name = "sabag.ch"
    close_down = False
    config = {}
    isLive = env("PRODUCTION")
    count = 0

    def __init__(self, _config=None, *args, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == "True":
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        config = {}
        config["SourceKey"] = "sabag.ch"
        config["BaseUrl"] = "https://www.sabag.ch"
        config["StartUrl"] = "https://www.sabag.ch/de/stellen?id=28829363573555"
        config["SourceCountry"] = "ch"
        config["CompanyName"] = "Sabag"
        config["LangCode"] = "de"
        config["Upload"] = True
        config["IsActive"] = True
        config["Custom"] = True
        config['DeleteAllJobsOnStart'] = True
        return config


    def start_requests(self):
        try:
            if self.config is None:
                CustomLogger.LogEvent(self.name, "No Config Read")
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], " Crawler Started")
                meta = {
                    "playwright": True,
                    "playwright_context": "new",
                    "playwright_include_page": True,
                    "playwright_page_methods": [
                        PageMethod("wait_for_timeout", 3000),
                    ]
                }
                yield scrapy.Request(self.config["StartUrl"], method='GET', callback=self.parse_job, meta=meta)
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()


    def parse_job(self, response):
        try:
            pass
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()
