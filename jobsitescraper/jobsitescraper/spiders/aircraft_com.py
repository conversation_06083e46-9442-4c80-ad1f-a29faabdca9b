import re
import sys
import traceback
from datetime import datetime, timezone
import scrapy
from bs4 import BeautifulSoup
from scrapy_playwright.page import PageMethod
from jobsitescraper.log_manager import CustomLogger
from jobsitescraper.utils import env


class ForboComSpider(scrapy.Spider):
    name = "aircraft.com"
    close_down = False
    config = {}
    i = 0
    page_num = 1
    total = 0
    count = -1
    isLive = env("PRODUCTION")

    def __init__(self, _config=None, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == "True":
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        config = {}
        config["SourceKey"] = "aircraft.com"
        config["BaseUrl"] = "https://www.pilatus-aircraft.com"
        config[
            "StartUrl"] = "https://www.pilatus-aircraft.com/de/career/jobs"
        config["CompanyName"] = "Pilatus Flugzeugwerke"
        config["SourceCountry"] = "ch"
        config["LangCode"] = "de"
        config["Upload"] = True
        config["IsActive"] = True
        config["Custom"] = True
        config["MaxPagesToCrawl"] = 10
        config["MaxJobsToCrawl"] = 500
        config["RecentJobs"] = True
        return config

    def start_requests(self):
        try:
            if self.config is None:
                CustomLogger.LogEvent(self.name, "No Config Read")
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], "Crawler Started")
                if self.config["RecentJobs"]:
                    CustomLogger.LogEvent(self.config["SourceKey"], "Recent Data thread started")
                    yield scrapy.Request(
                        url=self.config["StartUrl"],
                        method='GET',
                        callback=self.parse_recent,
                        meta={
                            "playwright": True,
                            "playwright_context": "new",
                            "playwright_page_methods": [
                                PageMethod("wait_for_timeout", 17000),
                                PageMethod("evaluate", "window.scrollBy(0, document.body.scrollHeight)"),
                                PageMethod("wait_for_selector", ".headline_slide_in")
                            ]
                        }
                    )
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    async def parse_recent(self, response):
        try:
            links = response.xpath(
                '//div[@class="item_container reveal_container jobs_overview"]/a')
            for i in links:
                link = i.xpath('.//@href').get()
                title = i.xpath('.//span[2]/span[1]/text()').get()
                loc = i.xpath('.//span[2]/span[3]/text()').get()
                href = self.config['BaseUrl'] + link
                meta = {
                    "title": title,
                    "loc": loc,
                    "playwright": True,
                    "playwright_context": "new",
                    "playwright_page_methods": [
                        PageMethod("wait_for_timeout", 17000),
                    ]
                }
                yield scrapy.Request(url=href, callback=self.parse_job, meta=meta)
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_job(self, response):
        try:
            jobTitle = response.meta['title']
            if jobTitle is not None:
                jobTitle = jobTitle.strip()
            else:
                jobTitle = ''

            jobLocation = response.meta['loc']
            if jobLocation is not None:
                jobLocation = jobLocation.strip()
            else:
                jobLocation = ''

            website_text = response.body.decode("UTF-8")
            jobs_soup = BeautifulSoup(website_text.replace("<", " <"), "html.parser")

            description = jobs_soup.find('div', {"class": "space-margin-bottom-200"})
            if description is not None:
                cleanContent = re.sub('\s+', ' ', description.get_text())
                rawContent = re.sub('\s+', ' ', description.decode_contents())
            else:
                cleanContent = ''
                rawContent = ''

            ad = {}
            ad['JobTitle'] = jobTitle
            ad['JobLocation'] = jobLocation
            ad['CompanyName'] = self.config["CompanyName"]
            ad['CompanyLogoFileURL'] = "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRcCrUvONwPeBYfN_GkV9jIeJ67GzctAZr8_A&s"
            ad["SourceURL"] = response.url
            ad['SourceCountry'] = self.config["SourceCountry"]
            ad['SourceKey'] = self.config["SourceKey"]
            ad['SourceLangCode'] = self.config["LangCode"]
            ad['CrawlTimestamp'] = datetime.now(timezone.utc).astimezone().isoformat()
            ad['SourceUID'] = response.url
            ad['CleanContent'] = cleanContent
            ad['RawContent'] = rawContent
            ad['PostedDate'] = datetime.now(timezone.utc).astimezone().isoformat()
            emailList = re.findall(
                '\S+@\S+', cleanContent.strip("\n"))
            phoneList = re.findall(r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]',
                                   cleanContent.strip("\n").replace('\u00a0', ' '))
            if len(emailList) > 0:
                _email = emailList[0]
                ad['JobContactEmails'] = _email
            if len(phoneList) > 0:
                for i in range(len(phoneList)):
                    phone = phoneList[i].strip().strip('(').strip(')')
                    if len(phone) > 0:
                        ad['JobContactPhone'] = phone

            if self.config["Upload"] is True:
                self.count += 1
                if self.count >= self.config["MaxJobsToCrawl"]:
                    self.crawler.engine.close_spider(self, 'Scraped jobs.')
                else:
                    yield ad
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], "Scraped But not uploaded")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def close(self, reason):
        try:
            CustomLogger.LogEvent(self.config["SourceKey"], f"Crawler Stopped, Total Jobs: {self.count}")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
