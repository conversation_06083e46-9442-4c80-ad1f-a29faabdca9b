from jobsitescraper.log_manager import CustomLogger
import scrapy,json,re
from bs4 import BeautifulSoup
from datetime import datetime,timezone
import sys, traceback
from jobsitescraper.utils import env
from scrapy.exceptions import CloseSpider

class amazon_CrawlingManager(scrapy.Spider):
    name = "amazon.com"
    close_down = False
    p=0
    i=0
    total=0
    count = 0
    isLive = env("PRODUCTION")
   
    def __init__(self, _config=None, *args, **kwargs):
        if self.isLive == "True":
            self.config = _config    
        else:
            self.config = self.get_config()

    def get_config(self):
        config = {}
        config["SourceKey"] = "amazon.com"
        config["BaseUrl"] = "https://www.amazon.jobs"
        config["StartUrl"] = "https://www.amazon.jobs/en/search.json?offset=[p]&result_limit=10"
        config["CompanyLogoFileURL"] = "https://m.media-amazon.com/images/G/01/gc/designs/livepreview/a_generic_white_10_us_noto_email_v2016_us-main._CB627448186_.png"
        config["CompanyName"] = "Amazon.com Services LLC"
        config["SourceCountry"] = "ch"
        config["LangCode"] = "de"
        config["Upload"] = True
        config["IsActive"] = True
        config["Custom"] =True
        config["MaxPagesToCrawl"] = 10
        config["MaxJobsToCrawl"] = 500
        config['DeleteAllJobsOnStart'] = False
        return config
        
# Starting request
    def start_requests(self):
        try: 
            if self.config is None:    
                CustomLogger.LogEvent(self.name,   "No Config Read")
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], " Crawler Started")
                yield scrapy.Request(self.config["StartUrl"].replace("[p]", str(self.p)), method='GET', callback=self.parse)   
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()


    def parse(self, response):
        try:
            try:
                _total = json.loads(response.body)
                totalcount = _total['hits']
                self.total =  int(totalcount)
            except:
                CustomLogger.LogEvent(self.config["SourceKey"], "Pagination Issue")
                return
            _limit = 10
            while self.i <=self.total:
                yield response.follow(self.config["StartUrl"].replace("[p]", str(self.p)), callback=self.parse_json, dont_filter=True)
                self.i += _limit
                self.p += 10
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()
    
    def parse_json(self, response):
        try:
            data = json.loads(response.body)
            if len(data) > 0:
                for i in range(len(data["jobs"])):
                    url = data["jobs"][i]["job_path"]
                    completeUrl = self.config['BaseUrl'] + str(url)
                    title= data["jobs"][i]["title"]
                    jobid= data["jobs"][i]["id_icims"]
                    jobtype= data["jobs"][i]["job_category"]
                    jobcontract= data["jobs"][i]["job_schedule_type"]
                    location= data["jobs"][i]["location"]
                    postedDate= data["jobs"][i]["posted_date"]
                    meta={
                        "title":title,
                        "jobid":jobid,
                        "jobtype":jobtype,
                        "jobcontract":jobcontract,
                        "location":location,
                        "postedDate":postedDate
                    }
                    yield scrapy.Request(completeUrl, callback=self.parse_job,meta=meta)
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], "No job links Found") 
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush() 

    def parse_job(self, response,):
        if self.close_down == True:
            raise CloseSpider("took dwon by analyzer")
        try:
            jobTitle = response.request.meta["title"]
            if jobTitle is not None:
                jobTitle = jobTitle.strip()
            else:
                jobTitle = ''
            jobLocation = response.request.meta["location"]
            if jobLocation is not None:
                jobLocation = jobLocation.strip()
            else:
                jobLocation = ''
            jobid = response.request.meta["jobid"]
            if jobid is not None:
                jobid = jobid.strip()
            else:
                jobid = response.url
            jobtype = response.request.meta["jobtype"]
            if jobtype is not None:
                jobtype = jobtype.strip()
            else:
                jobtype = ''
            jobcontract = response.request.meta["jobcontract"]
            if jobcontract is not None:
                jobcontract = jobcontract.strip()
            else:
                jobcontract = ''
            _date = response.request.meta["postedDate"]
            postedDate = datetime.strptime(_date, '%B %d, %Y').isoformat()
            if postedDate is not None:
                postedDate = postedDate
            else:
                postedDate = ''
            website_text = response.body.decode("utf-8")    
            jobs_soup = BeautifulSoup(website_text.replace("<", " <"), "html.parser")
            description = jobs_soup.find(
                'div',{"class":"col-12 col-md-7 col-lg-8 col-xl-9"}).find('div',{"class":"content"})
            if description is not None:
                cleanContent = re.sub('\s+', ' ', description.get_text())
                rawContent = re.sub('\s+', ' ', description.decode_contents())
            else:
                cleanContent = ''
                rawContent = ''
            ad = {}
            ad['JobTitle'] = jobTitle
            ad['JobLocation'] = jobLocation
            ad['CompanyName'] = self.config["CompanyName"]
            ad['PostedDate'] = postedDate
            ad['JobContractTypeText'] = jobcontract
            ad['JobTypeText'] = jobtype
            ad["SourceURL"] = response.url
            ad['SourceCountry'] = self.config["SourceCountry"]
            ad['SourceKey'] = self.config["SourceKey"]
            ad['SourceLangCode'] = self.config["LangCode"]
            ad['CrawlTimestamp'] = datetime.now(timezone.utc).astimezone().isoformat()
            ad['CleanContent'] = cleanContent
            ad['RawContent'] = rawContent
            ad['CompanyLogoFileURL'] = self.config["CompanyLogoFileURL"]
            ad['SourceUID'] = jobid

            emailList = re.findall(
                '\S+@\S+', cleanContent.strip("\n"))
            phoneList = re.findall(
                r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]', cleanContent.strip("\n").replace('\u00a0',' '))
            if len(emailList) >0:
                _email = emailList[0]
                ad['JobContactEmails'] = _email    
            if len(phoneList) >0:
                for i in range(len(phoneList)):
                    phone=phoneList[i].strip().strip('(').strip(')')
                    if len(phone)>0:
                        ad['JobContactPhone'] = phone

            if self.config["Upload"] is True:
                self.count += 1
                if self.count >= self.config["MaxJobsToCrawl"]:
                    self.crawler.engine.close_spider(self, 'Scraped jobs.')
                else:
                    yield ad
            else:
                CustomLogger.LogEvent(self.config["SourceKey"],   "Scraped But not uploaded")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def close(self, reason):
        try:
            CustomLogger.LogEvent(self.config["SourceKey"], f"Crawler Stopped, Total Jobs: {self.count}")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))