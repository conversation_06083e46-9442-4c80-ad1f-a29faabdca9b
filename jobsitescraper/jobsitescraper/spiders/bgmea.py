import sys
import traceback
from jobsitescraper.log_manager import CustomLogger
import scrapy
from jobsitescraper.utils import env


class bgmeaComSpider(scrapy.Spider):
    name = "bgmea.com"
    close_down = False
    config = {}
    page_num = 1
    i = 0
    count = -1
    available_pages = 1
    isLive = env("PRODUCTION")

    def __init__(self, _config=None, *args, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == "True":
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        config = {}
        config["SourceKey"] = "bdjobs.com"
        config["BaseUrl"] = "https://jobs.bdjobs.com/"
        config["StartUrl"] = "https://bgmea.com.bd/page/member-list?page=[page_num]"
        config["SourceCountry"] = "bd"
        config["LangCode"] = "en"
        config["Upload"] = True
        config["IsActive"] = True
        config["Custom"] = True
        config['HasError'] = False
        config["Depth"] = 1
        config["MaxPagesToCrawl"] = 200
        config["MaxJobsToCrawl"] = 500
        config["RecentJobs"] = True
        config['DeleteAllJobsOnStart'] = False
        return config

    def start_requests(self):
        try:
            if self.config is None:
                CustomLogger.LogEvent(self.name, "No Config Read")
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], " Crawler Started")
                if self.config["RecentJobs"]:
                    CustomLogger.LogEvent(self.config["SourceKey"], "Recent Data thread started")
                    yield scrapy.Request(self.config["StartUrl"].replace("[page_num]", str(self.page_num)),
                                         callback=self.parse_recent, method="GET")
                else:
                    CustomLogger.LogEvent(self.config["SourceKey"], "All Data thread started")
                    yield scrapy.Request(self.config["StartUrl"].replace("[page_num]", str(self.page_num)),
                                         callback=self.parse_recent, method="GET")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], f"{str(e)}")
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_recent(self, response):
        try:
            for i in response.xpath('//a[@class="btn btn-sm btn-info"]'):
                link = i.xpath(".//@href").get()
                yield scrapy.Request(link, callback=self.parse_detail)

            self.page_num += 1
            if 0 < self.page_num <= self.config["MaxPagesToCrawl"]:
                yield response.follow(self.config["StartUrl"].replace("[page_num]", str(self.page_num)),
                                      callback=self.parse_recent)

        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], f"{str(e)}")
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_detail(self, response):
        company = response.xpath('//table[@class="table table-responsive-lg table-sm table-striped"]/thead/tr/th/strong/text()').get()
        if company is not None:
            company = company
        else:
            company = ''

        name = response.xpath('//th[text()="Name"]/parent::tr/parent::thead/following-sibling::tbody/tr/td[2]/text()').get()
        if name is not None:
            name = name
        else:
            name = ''
        email = response.xpath('//th[text()="Email"]/parent::tr/parent::thead/following-sibling::tbody/tr/td[4]/text()').get()
        if email is not None:
            email = email
        else:
            email = ''
        phone = response.xpath('//th[text()="Mobile No."]/parent::tr/parent::thead/following-sibling::tbody/tr/td[3]/text()').get()
        if phone is not None:
            phone = phone
        else:
            phone = ''
        item = {}
        item['url'] = response.url
        item['company'] = company
        item['name'] = name
        item['email'] = email
        item['phone'] = phone
        yield item