import re
import sys
import traceback
from datetime import datetime, timezone
import scrapy
from bs4 import BeautifulSoup
from scrapy_playwright.page import PageMethod
from jobsitescraper.log_manager import CustomLogger
from jobsitescraper.utils import env


class CareersElcaChSpider(scrapy.Spider):
    name = "careers.elca.ch"
    close_down = False
    config = {}
    i = 0
    page_num = 1
    total = 0
    count = -1
    isLive = env("PRODUCTION")

    def __init__(self, _config=None, *args, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == "True":
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        config = {}
        config["SourceKey"] = "careers.elca.ch"
        config["BaseUrl"] = "https://iaaras.fa.ocs.oraclecloud.com"
        config[
            "StartUrl"] = "https://iaaras.fa.ocs.oraclecloud.com/hcmUI/CandidateExperience/en/sites/CX_1/requisitions"
        config["CompanyName"] = "ELCA Informatique SA"
        config["SourceCountry"] = "ch"
        config["LangCode"] = "de"
        config["Upload"] = True
        config["IsActive"] = True
        config["Custom"] = False
        config["MaxPagesToCrawl"] = 10
        config["MaxJobsToCrawl"] = 500
        config["RecentJobs"] = True
        config['DeleteAllJobsOnStart'] = True
        return config

    def start_requests(self):
        try:
            if self.config is None:
                CustomLogger.LogEvent(self.name, "No Config Read")
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], "Crawler Started")
                if self.config["RecentJobs"]:
                    CustomLogger.LogEvent(self.config["SourceKey"], "Recent Data thread started")
                    yield scrapy.Request(
                        url=self.config["StartUrl"],
                        method='GET',
                        callback=self.parse_recent,
                        meta={
                            "playwright": True,
                            "playwright_context": "new",
                            "playwright_page_methods": [
                                PageMethod("evaluate", "window.scrollBy(0, document.body.scrollHeight)"),
                                PageMethod("wait_for_timeout", 17000),
                                PageMethod("wait_for_selector", ".job-list-item__link"),
                            ],
                        }
                    )
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_recent(self, response):
        try:
            links = response.xpath(
                '//li[@data-qa="searchResultItem"]')
            for i in links:
                job_link = i.xpath('.//div/a/@href').get()
                meta = {"playwright": True,
                        "playwright_context": "new",
                        "playwright_page_methods": [
                            PageMethod("wait_for_timeout", 17000),
                        ]
                        }
                yield scrapy.Request(url=job_link, callback=self.parse_job, meta=meta)

        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_job(self, response):
        try:
            jobTitle = response.xpath('//h1[@class="heading job-details__title"]/text()').get()
            if jobTitle is not None:
                jobTitle = jobTitle.strip()
            else:
                jobTitle = ''

            jobLocation = response.xpath('//span[@data-bind="html: primaryLocation"]/text()').get()
            if jobLocation is not None:
                jobLocation = jobLocation.strip()
            else:
                jobLocation = ''

            website_text = response.body.decode("UTF-8")
            jobs_soup = BeautifulSoup(website_text.replace("<", " <"), "html.parser")

            description = jobs_soup.find('div', {"class": "job-details-wrapper"})
            if description is not None:
                cleanContent = re.sub('\s+', ' ', description.get_text())
                rawContent = re.sub('\s+', ' ', description.decode_contents())
            else:
                cleanContent = ''
                rawContent = ''

            ad = {}
            ad['JobTitle'] = jobTitle
            ad['JobLocation'] = jobLocation
            ad['CompanyName'] = "ELCA Informatique SA"
            ad['CompanyLogoFileURL'] = "https://careers.elca.ch/sites/default/files/logo-header.svg?v=1.1.4"
            ad["SourceURL"] = response.url
            ad['SourceCountry'] = self.config["SourceCountry"]
            ad['SourceKey'] = self.config["SourceKey"]
            ad['SourceLangCode'] = self.config["LangCode"]
            ad['CrawlTimestamp'] = datetime.now(timezone.utc).astimezone().isoformat()
            ad['SourceUID'] = response.url
            ad['CleanContent'] = cleanContent
            ad['RawContent'] = rawContent
            ad['PostedDate'] = datetime.now(timezone.utc).astimezone().isoformat()
            emailList = re.findall(
                '\S+@\S+', cleanContent.strip("\n"))
            phoneList = re.findall(r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]',
                                   cleanContent.strip("\n").replace('\u00a0', ' '))
            if len(emailList) > 0:
                _email = emailList[0]
                ad['JobContactEmails'] = _email
            if len(phoneList) > 0:
                for i in range(len(phoneList)):
                    phone = phoneList[i].strip().strip('(').strip(')')
                    if len(phone) > 0:
                        ad['JobContactPhone'] = phone

            if self.config["Upload"] is True:
                self.count += 1
                if self.count >= self.config["MaxJobsToCrawl"]:
                    self.crawler.engine.close_spider(self, 'Scraped jobs.')
                else:
                    yield ad

            else:
                CustomLogger.LogEvent(self.config["SourceKey"], "Scraped But not uploaded")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def close(self, reason):
        try:
            CustomLogger.LogEvent(self.config["SourceKey"], f"Crawler Stopped, Total Jobs: {self.count}")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
