from jobsitescraper.log_manager import CustomLogger
import scrapy, re
import traceback, sys
from jobsitescraper.utils import env
from bs4 import BeautifulSoup
from datetime import datetime, timezone


class at_jobsora_com_CrawlingManager(scrapy.Spider):
    name = "at.jobsora.com"
    close_down = False
    config = {}
    i = 0
    page_num = 1
    total = 0
    count = -1
    isLive = env("PRODUCTION")

    def __init__(self, _config=None, *args, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == "True":
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        config = {}
        config["SourceKey"] = "at.jobsora.com"
        config["BaseUrl"] = "https://at.jobsora.com"
        config["StartUrl"] = "https://at.jobsora.com/jobs?page=[page_num]"
        config["CompanyLogoFileURL"] = "https://at.jobsora.com/static/assets/15ba7c3f/img/general/index-logo.svg"
        config["CompanyName"] = "Jobsora"
        config["SourceCountry"] = "ch"
        config["LangCode"] = "de"
        config["Upload"] = True
        config["IsActive"] = True
        config["Custom"] = True
        config["MaxPagesToCrawl"] = 10
        config["MaxJobsToCrawl"] = 100
        config["RecentJobs"] = True
        config['DeleteAllJobsOnStart'] = False
        return config
        
    def start_requests(self):
        try:
            if self.config is None:
                CustomLogger.LogEvent(self.name, "No Config Read")
            else:
                CustomLogger.LogEvent(
                    self.config["SourceKey"], " Crawler Started")
                if self.config["RecentJobs"]:
                    CustomLogger.LogEvent(self.config["SourceKey"], "Recent Data thread started")
                    yield scrapy.Request(self.config["StartUrl"].replace("[page_num]", str(self.page_num)),
                                         method='GET', callback=self.parse_all)
                else:
                    CustomLogger.LogEvent(self.config["SourceKey"], "All Data thread started")
                    yield scrapy.Request(self.config["StartUrl"].replace("[page_num]", str(self.page_num)),
                                         method='GET', callback=self.parse_all)
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def extract_job_details(self, htmls):
        try:
            job_link_pattern = re.search(r'data-href=\"([^\"]+)\"', htmls)
            info_items = re.findall(r'<div class=\"c-job-item__info-item\">\s*<svg[^>]*>.*?</svg>([^<]+)', htmls)
            company_name = info_items[0].strip() if len(info_items) > 0 else None
            location = info_items[1].strip() if len(info_items) > 1 else None

            return {
                "job_link": job_link_pattern.group(1) if job_link_pattern else None,
                "company_name": company_name,
                "location": location
            }
        except Exception as e:
            print(f"Error extracting job details: {e}")
            return None

    def parse_all(self, response):
        try:
            for article in response.xpath(
                    '//article[@class="js-listing-item c-job-item w-gap-lg js-vacancy-snippet js-clickable"]'):
                title = article.xpath('.//header/h2/a/text()').get()
                htmls = article.get()
                job_details = self.extract_job_details(htmls)
                link = job_details["job_link"]
                company = job_details['company_name']
                location = job_details['location']
                meta = {'company': company, 'loc': location, 'title': title}
                yield response.follow(link, callback=self.parse_job, meta=meta)

            self.page_num += 1
            if 0 < self.page_num <= self.config["MaxPagesToCrawl"]:
                next_page = f'https://ch.jobsora.com/jobs?page={self.page_num}'
                yield response.follow(next_page, callback=self.parse_all)

        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def clean_html(self, text):
        cleanr = re.compile('<.*?>')
        cleantext = re.sub(cleanr, '', text)
        return cleantext

    def parse_job(self, response):
        try:
            company = response.meta['company']
            if company is not None:
                company = company
            else:
                company = ''

            jobLocation = response.meta['loc']
            if jobLocation is not None:
                jobLocation = self.clean_html(jobLocation)
            else:
                jobLocation = ''

            jobTitle = response.meta['title']
            if jobTitle is not None:
                jobTitle = jobTitle.strip()
            else:
                jobTitle = ''

            website_text = response.body.decode("UTF-8")
            jobs_soup = BeautifulSoup(website_text.replace("<", " <"), "html.parser")
            description = jobs_soup.find('div', {"class": "c-main-box c-main-box--space-free"})
            if description is not None:
                cleanContent = re.sub('\s+', ' ', description.get_text())
                rawContent = re.sub('\s+', ' ', description.decode_contents())
            else:
                cleanContent = ''
                rawContent = ''

            ad = {}
            ad['JobTitle'] = jobTitle
            ad['CompanyName'] = company
            ad['JobLocation'] = jobLocation
            ad["SourceURL"] = response.url
            ad['SourceCountry'] = self.config["SourceCountry"]
            ad['SourceKey'] = self.config["SourceKey"]
            ad['SourceLangCode'] = self.config["LangCode"]
            ad['CompanyLogoFileURL'] = self.config["CompanyLogoFileURL"]
            ad['CrawlTimestamp'] = datetime.now(timezone.utc).astimezone().isoformat()
            ad['PostedDate'] = datetime.now(timezone.utc).astimezone().isoformat()
            ad['CleanContent'] = cleanContent
            ad['RawContent'] = rawContent
            ad['SourceUID'] = response.url

            emailList = re.findall(
                '\S+@\S+', cleanContent.strip("\n"))
            phoneList = re.findall(r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]',
                                   cleanContent.strip("\n").replace('\u00a0', ' '))
            if len(emailList) > 0:
                _email = emailList[0].replace('(', '').replace(')', '').replace(',', '')
                ad['JobContactEmails'] = _email
            if len(phoneList) > 0:
                for i in range(len(phoneList)):
                    phone = phoneList[i].strip().strip('(').strip(')')
                    if len(phone) > 0:
                        ad['JobContactPhone'] = phone

            if self.config["Upload"] is True:
                self.count += 1
                if self.count >= self.config["MaxJobsToCrawl"]:
                    self.crawler.engine.close_spider(self, 'Scraped jobs.')
                else:
                    yield ad
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], "Scraped But not uploaded")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def close(self, reason):
        try:
            CustomLogger.LogEvent(self.config["SourceKey"], f"Crawler Stopped, Total Jobs: {self.count}")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()
