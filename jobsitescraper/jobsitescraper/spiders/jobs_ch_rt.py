from scrapy_playwright.page import PageMethod
from jobsitescraper.log_manager import CustomLogger
from jobsitescraper.utils import env
from bs4 import BeautifulSoup
from datetime import datetime, timezone
import sys, traceback
import scrapy, re
from scrapy.exceptions import CloseSpider


class JobsCHRtCrawlingManager(scrapy.Spider):
    name = "jobs.ch_all"
    close_down = False
    count = 0
    page_num = 1
    isLive = env("PRODUCTION")

    def __init__(self, _config=None, *args, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == "True":
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        config = {}
        config["SourceKey"] = "jobs.ch"
        config["BaseUrl"] = "https://www.jobs.ch"
        config["StartUrl"] = "https://www.jobs.ch/en/companies/?page=[page_num]&term="
        config["SourceCountry"] = "ch"
        config["LangCode"] = "de"
        config['CompanyName'] = 'jobs.ch'
        config["Upload"] = True
        config["IsActive"] = True
        config["Custom"] = True
        config["Depth"] = 1
        config["MaxPagesToCrawl"] = 50
        config['MaxJobsToCrawl'] = 500
        config["RecentJobs"] = True
        config['DeleteAllJobsOnStart'] = False
        return config


    def start_requests(self):
        try:
            if self.config is None:
                CustomLogger.LogEvent(self.name, "No Config Read")
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], " Crawler Started")
                yield scrapy.Request(self.config["StartUrl"].replace("[page_num]", str(self.page_num)), method='GET', callback=self.parse_all)
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_all(self, response):
        try:
            links = response.xpath('//div[@class="d_flex jc_space-between"]')
            for i in links:
                link = i.xpath('.//a/@href').get()
                if link is not None:
                    href = self.config["BaseUrl"] + link
                    meta = {"playwright": True,
                            "playwright_context": "new",
                            "playwright_page_methods": [
                                PageMethod("wait_for_timeout", 17000),
                            ]
                            }
                    yield response.follow(href, callback=self.parse_all_jobs, meta=meta)
            self.page_num += 1
            if self.page_num <= self.config["MaxPagesToCrawl"]:
                next_page = self.config["StartUrl"].replace("[page_num]", str(self.page_num))
                yield scrapy.Request(url=next_page, method='GET', callback=self.parse_all)
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_all_jobs(self, response):
        try:
            for i in response.xpath('//a[@data-cy="job-link"]'):
                link = i.xpath('.//@href').get()
                title = i.xpath('.//div/div[2]/span/text()').get()
                loc = i.xpath('.//div/div[3]/div[1]/p[1]/text()').get()
                if link is not None:
                    href = self.config["BaseUrl"] + link
                    meta = {
                            "title": title,
                            "loc": loc,
                            "playwright": True,
                            "playwright_context": "new",
                            "playwright_page_methods": [
                                PageMethod("wait_for_timeout", 17000),
                            ]
                            }
                    yield response.follow(href, callback=self.parse_job, meta=meta)
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_job(self, response):
        try:
            jobTitle = response.meta['title']
            if jobTitle is not None:
                jobTitle = jobTitle.strip()
            else:
                jobTitle = ''

            jobLocation = response.meta['loc']
            if jobLocation is not None:
                jobLocation = jobLocation.strip()
                jobLocation = jobLocation.replace('-near', '').replace('Region', '').replace('Stadt', '')
                jobLocation = jobLocation.split('/')[0]
            else:
                jobLocation = ''

            Logo = response.xpath('//div[@data-cy="vacancy-logo"]/div/picture/img/@src').get()
            if Logo is not None:
                Logo = Logo
            else:
                Logo = ''

            CompanyName = response.xpath(
                '//a[@data-cy="company-link"]/span/text()').get()
            if CompanyName is not None:
                CompanyName = CompanyName
            else:
                CompanyName = ''

            website_text = response.body.decode("utf-8")
            jobs_soup = BeautifulSoup(website_text.replace("<", " <"), "html.parser")
            description = jobs_soup.find('div', {"data-cy": "vacancy-description"})
            if description is not None:
                for tag in description.find_all(['a', 'script', 'style', 'meta', 'link', 'svg']):
                    tag.decompose()
                cleanContent = re.sub('\s+', ' ', description.get_text())
                rawContent = re.sub('\s+', ' ', description.decode_contents())
            else:
                cleanContent = ''
                rawContent = ''
            ad = {}
            ad['JobTitle'] = jobTitle
            ad['JobLocation'] = jobLocation
            ad['companyName'] = CompanyName
            ad['CompanyLogoFileURL'] = Logo
            ad["SourceURL"] = response.url
            ad['SourceUID'] = response.url
            ad['SourceCountry'] = self.config["SourceCountry"]
            ad['SourceKey'] = self.config["SourceKey"]
            ad['SourceLangCode'] = self.config["LangCode"]
            ad['CrawlTimestamp'] = datetime.now(timezone.utc).astimezone().isoformat()
            ad['CleanContent'] = cleanContent
            ad['RawContent'] = rawContent
            ad['PostedDate'] = datetime.now(timezone.utc).astimezone().isoformat()

            emailList = re.findall(
                '\S+@\S+', cleanContent.strip("\n"))
            phoneList = re.findall(
                r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]', cleanContent.strip("\n").replace('\u00a0', ' '))
            if len(emailList) > 0:
                _email = emailList[0]
                ad['JobContactEmails'] = _email
            if len(phoneList) > 0:
                for i in range(len(phoneList)):
                    phone = phoneList[i].strip().strip('(').strip(')')
                    if len(phone) > 0:
                        ad['JobContactPhone'] = phone
            if self.config["Upload"] is True:
                self.count += 1
                if self.count >= self.config["MaxJobsToCrawl"]:
                    self.crawler.engine.close_spider(self, 'Scraped jobs.')
                else:
                    yield ad
            else:
                CustomLogger.LogEvent(
                    self.config["SourceKey"], "Scraped But not uploaded")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def close(self, reason):
        try:
            CustomLogger.LogEvent(
                self.config["SourceKey"], f"Crawler Stopped, Total Jobs: {self.count}")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
