from jobsitescraper.log_manager import CustomLogger
import scrapy, re
import traceback, sys
from jobsitescraper.utils import env
from scrapy.exceptions import CloseSpider
from bs4 import BeautifulSoup
from datetime import datetime, timezone


class au_jora_com_CrawlingManager(scrapy.Spider):
    name = "au.jora.com"
    close_down = False
    config = {}
    i = 0
    page_num = 1
    total = 0
    count = -1
    isLive = env("PRODUCTION")

    def __init__(self, _config=None, *args, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == "True":
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        config = {}
        config["SourceKey"] = "au.jora.com"
        config["BaseUrl"] = "https://au.jora.com"
        config["StartUrl"] = "https://au.jora.com/j?l=&p=[page_num]"
        config["CompanyLogoFileURL"] = "https://bd.jora.com/jora-social-sharing-banner.png"
        config["SourceCountry"] = "id"
        config["LangCode"] = "en"
        config["Upload"] = True
        config["IsActive"] = True
        config["Custom"] = True
        config["MaxPagesToCrawl"] = 10
        config["MaxJobsToCrawl"] = 500
        config["RecentJobs"] = True
        return config

    def start_requests(self):
        try:
            if self.config is None:
                CustomLogger.LogEvent(self.name, "No Config Read")
            else:
                CustomLogger.LogEvent(
                    self.config["SourceKey"], " Crawler Started")
                if self.config["RecentJobs"]:
                    CustomLogger.LogEvent(
                        self.config["SourceKey"], "Recent Data thread started")
                    yield scrapy.Request(self.config["StartUrl"].replace("[page_num]", str(self.page_num)),
                                         method='GET', callback=self.extract_joblinks)
                else:
                    CustomLogger.LogEvent(
                        self.config["SourceKey"], "All Data thread started")
                    yield scrapy.Request(self.config["StartUrl"].replace("[page_num]", str(self.page_num)),
                                         method='GET', callback=self.extract_joblinks)
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()


    def extract_joblinks(self, response):
        try:
            links = response.xpath('//a[@class="job-link -mobile-only"]')
            for i in links:
                job_link = i.xpath(
                    './/@href').get()
                href = self.config["BaseUrl"] + str(job_link)
                yield response.follow(href, callback=self.parse_job)

            self.page_num += 1
            if self.page_num <= self.config["MaxPagesToCrawl"]:
                next_page = self.config["StartUrl"].replace("[page_num]", str(self.page_num))
                yield scrapy.Request(url=next_page, method='GET', callback=self.extract_joblinks)
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_job(self, response, ):

        if self.close_down == True:
            raise CloseSpider("took down by analyzer")
        try:
            ad = {}
            jobTitle = response.xpath('//h1[@class="job-title heading -size-xxlarge -weight-700"]/text()').get()
            if jobTitle is not None:
                jobTitle = jobTitle.strip()
            else:
                jobTitle = ''

            jobLocation = response.xpath('//span[@class="location"]/text()').get()
            if jobLocation is not None:
                jobLocation = jobLocation.replace('\xa0|\xa0', '')
            else:
                jobLocation = ''

            CompanyName = response.xpath('//span[@class="company"]/text()').get()
            if CompanyName is not None:
                CompanyName = CompanyName.strip()
            else:
                CompanyName = ''


            website_text = response.body.decode("utf-8")
            jobs_soup = BeautifulSoup(website_text.replace("<", " <"), "html.parser")

            description = jobs_soup.find('div', {"class": "job-view-content grid-content"})
            if description is not None:
                cleanContent = re.sub('\s+', ' ', description.get_text())
                rawContent = re.sub('\s+', ' ', description.decode_contents())
            else:
                cleanContent = ''
                rawContent = ''

            ad['JobTitle'] = jobTitle
            ad['JobLocation'] = jobLocation
            ad['CompanyLogoFileURL'] = self.config['CompanyLogoFileURL']
            ad['CompanyName'] = CompanyName
            ad["SourceURL"] = response.url
            ad['SourceCountry'] = self.config["SourceCountry"]
            ad['SourceKey'] = self.config["SourceKey"]
            ad['SourceLangCode'] = self.config["LangCode"]
            ad['CrawlTimestamp'] = datetime.now(timezone.utc).astimezone().isoformat()
            ad['SourceUID'] = response.url
            ad['CleanContent'] = cleanContent
            ad['RawContent'] = rawContent
            ad['PostedDate'] = datetime.now(timezone.utc).astimezone().isoformat()

            emailList = re.findall(
                '\S+@\S+', cleanContent.strip("\n"))
            phoneList = re.findall(r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]',
                                   cleanContent.strip("\n").replace('\u00a0', ' '))
            if len(emailList) > 0:
                _email = emailList[0]
                ad['JobContactEmails'] = _email
            if len(phoneList) > 0:
                for i in range(len(phoneList)):
                    phone = phoneList[i].strip().strip('(').strip(')')
                    if len(phone) > 0:
                        ad['JobContactPhone'] = phone

            if self.config["Upload"] is True:
                self.count += 1
                if self.count >= self.config["MaxJobsToCrawl"]:
                    self.crawler.engine.close_spider(self, 'Scraped jobs.')
                else:
                    yield ad
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], "Scraped But not uploaded")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def close(self, reason):
        try:
            CustomLogger.LogEvent(self.config["SourceKey"], f"Crawler Stopped, Total Jobs: {self.count}")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
