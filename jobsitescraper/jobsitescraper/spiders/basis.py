import json
import scrapy
import re
from scrapy_playwright.page import PageMethod


class ExtractLinksSpider(scrapy.Spider):
    name = "basis"

    def start_requests(self):
        try:
            with open("data.json") as f:
                data = json.load(f)
                for item in data:
                    url = item.get("URL")
                    meta = {"playwright": True,
                            "playwright_context": "new",
                            "playwright_page_methods": [
                                PageMethod("wait_for_timeout", 17000),
                            ]
                            }
                    yield scrapy.Request(url=url, callback=self.parse_all, meta=meta)
        except Exception as e:
            print(f"Error: {e}")

    def parse_all(self, response):
        add = {}
        company_name = response.css('div.companyDetails h1::text').get()
        if company_name is not None:
            add['CompanyName'] = company_name.strip()
        else:
            add['CompanyName'] = ''

        location = response.css('div.companyDetails p::text').get()
        if location is not None:
            add['Location'] = location.strip()
        else:
            add['Location'] = ''

        email = response.xpath('//strong[text()="Email:"]/following-sibling::text()').get()
        if email is not None:
            add['Email'] = email.strip()
        else:
            add['Email'] = ''

        phone = response.xpath('//strong[text()="Phone:"]/following-sibling::text()').get()
        if phone is not None:
            add['Phone'] = phone.strip()
        else:
            add['Phone'] = ''

        add["source"] = response.url
        yield add
