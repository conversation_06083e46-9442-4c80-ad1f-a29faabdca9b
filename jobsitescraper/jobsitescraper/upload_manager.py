
import json
import traceback
from datetime import datetime
import requests
import sys
import redis
from langdetect import detect, DetectorFactory
import locationtagger
from deep_translator import GoogleTranslator
DetectorFactory.seed = 0
from jobsitescraper.utils import env
from unidecode import unidecode
import re


upload_list = []
class UploadManager:
    sourceKey = ""
    redis_host =""
    redis_port=""
    redis_password =""
    redis_client=""
    proxies={
        "http": f'http://{env("PROXY_USERNAME")}:{env("PROXY_PASSWORD")}@p.webshare.io:80/',
        "https":f'http://{env("PROXY_USERNAME")}:{env("PROXY_PASSWORD")}@p.webshare.io:80/'
    }
    
    def __init__(self, sourceKey):
        self.sourceKey = sourceKey
        self.redis_host = env("REDIS_HOST")
        self.redis_port = env("REDIS_PORT")
        self.redis_password = env("REDIS_PASSWORD")
        self.redis_client = redis.StrictRedis(host=self.redis_host, port=self.redis_port, password=self.redis_password, decode_responses=True)

    def Upload(self, ad):
        try:
            ad = self.GetAdLocation(ad)
            upload_list.append(ad)
            with open('data.json', 'w', encoding='utf8') as outfile:
                json.dump(upload_list, outfile,ensure_ascii=False)
            if (len(upload_list) >= 20):
                response = self.redis_client.rpush(
                    "jobs_data", *[json.dumps(data,ensure_ascii=False) for data in upload_list])

                print("\n################################################################################################################################################\n")
                sys.stdout.flush()
                print(str(datetime.now())+"  ["+self.sourceKey+"]" + " " + str(len(upload_list)) +
                      " Jobs added, Total jobs in redis: "+str(self.redis_client.llen("jobs_data")))
                sys.stdout.flush()
                print("\n################################################################################################################################################\n")
                sys.stdout.flush()
                upload_list.clear()
                return 0
        except Exception as e:
            print(e)
            sys.stdout.flush()

    def UploadRemaining(self):
        try:

            if (len(upload_list) > 0):

                response = self.redis_client.rpush(
                    "jobs_data", *[json.dumps(data,ensure_ascii=False) for data in upload_list])

                print("\n################################################################################################################################################\n")
                sys.stdout.flush()
                print(str(datetime.now())+"  ["+self.sourceKey+"]" + " " + str(len(upload_list)) +
                      " Jobs added, Total jobs in redis: "+str(self.redis_client.llen("jobs_data")))
                sys.stdout.flush()
                print("\n################################################################################################################################################\n")
                sys.stdout.flush()
                upload_list.clear()
                return 0
        except Exception as e:
            print(e)
            sys.stdout.flush()

    def GetAdLocation(self,ad):
        try:
            prohibit_location = [ "TBD", "Global", "International", "Corporate"]
            ignored_words = ["deutschschweiz"]
            query = ""
            loc_res = None
            loc_text = ""
            if "JobLocation"  in ad:
                resultwords  = [word for word in re.split("\W+",ad["JobLocation"]) if word.lower() not in ignored_words]
                loc_text = " ".join(resultwords)
                # print("loc_text ", loc_text)
            if any(ele in loc_text.replace("!@#$%^&*()[]{};:,./<>?\|`~-=_+", " ") for ele in prohibit_location)== False:
                loc_res = self.geo_locator(loc_text)
                # print(loc_text, " ",loc_res)
            if loc_res is None or len(loc_res) == 0:         
                if loc_text is None:
                    query = ad["CleanContent"]
                location = self.location_tagger(loc_text)
                query = location
                loc_res = self.geo_locator(query)
                # print(query, " ",loc_res)
            if loc_res is not None and len(loc_res) > 0:
                loc_data = loc_res[0]
                if loc_data is not None:
                    if "address" in loc_data:
                        address = loc_data["address"]
                        loc =None
                        if address is not None:
                            if "town" in address:
                                loc = address["town"]
                                ad["JobLocationCity"] = address["town"]                                
                            elif "city" in address:
                                loc = address["city"]
                                ad["JobLocationCity"] = address["city"]
                            elif "region" in address:
                                loc = address["region"]
                            if "postcode" in address:
                                ad["JobLocationPostalCode"] = address["postcode"]
                            if "state" in address:
                                ad["JobLocationLocality"] = address["state"]
                                if loc is None:
                                    loc = address["state"]
                            if "country" in address:
                                ad["JobLocationCountry"] = address["country"]
                                if loc is None:
                                    loc = address["country"]
                            if "country_code" in address:
                                ad["JobLocationCountryCode"] = address["country_code"]
                                ad["SourceCountry"] = address["country_code"]
                                ad["JobLocationText"]= ad["JobLocation"]
                                ad["JobLocation"] = loc
                    ad["GeoLat"] = loc_data["lat"]
                    ad["GeoLong"] = loc_data["lon"]
            lang =self.detect_lang(ad["CleanContent"])
            if lang is not None and len(lang)==2:
                ad["SourceLangCode"] = lang
        except Exception as e:
            print(traceback.format_exc())
            sys.stdout.flush()
        return ad


    def geo_locator(self, query):
        if (query is None or query == ''):
            return None
        else:  # api
            query = query.split("/")[len(query.split("/"))-1]
            query = query.replace(" ",",")
            query = unidecode(query)
            res =  self.redis_client.get("loc_query_"+query)
            if res is not None:
                # print("Location returning from Redis")
                return(json.loads(res))
            url = "https://nominatim.openstreetmap.org/?addressdetails=1&q=" + \
                query+"&format=json&limit=1"
            # print(url)
            try:
                response = requests.get(
                    url,  headers={"accept-language": "en"}).json()  # look
                self.redis_client.set("loc_query_"+query, json.dumps(response))
                return response
            except  Exception as e:
                print(traceback.format_exc())
                sys.stdout.flush()
                return None

    def detect_lang(self, content):
        try:
            if (content is None or content == ''):
                return None
            return detect(content[0:500])
        except Exception as e:
            print(traceback.format_exc())
            sys.stdout.flush()
            return None

    def location_tagger(self, text):
        if (text is None or text == ''):
            return None
        text = GoogleTranslator(source='auto', target='en').translate(
            text[0:500])  # used for more accurate result
        text = unidecode(text)
        place_entity = locationtagger.find_locations(text=text[0:500])
        location = ""
        if place_entity is not None:
            # print(place_entity.cities, place_entity.countries)
            if len(place_entity.cities) > 0:
                location += place_entity.cities[0]+","
            if len(place_entity.countries) > 0:
                location += place_entity.countries[0]
        return location

