import jobsitescraper.proxies as p
import jobsitescraper.config_manager as conf
import tldextract


BOT_NAME = 'jobsitescraper'
SPIDER_MODULES = ['jobsitescraper.spiders']
NEWSPIDER_MODULE = 'jobsitescraper.spiders'
USER_AGENT = 'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.75 Safari/537.36'


LOG_LEVEL = "DEBUG"
LOG_ENABLED = True
# LOG_FILE = "scrapy_log.log"

ROBOTSTXT_OBEY = False

DOWNLOAD_DELAY = 1
# CONCURRENT_REQUESTS = 2
# CONCURRENT_REQUESTS_PER_DOMAIN = 1
#
# AUTOTHROTTLE_ENABLED = True
# AUTOTHROTTLE_START_DELAY = 5
# AUTOTHROTTLE_MAX_DELAY = 60
# AUTOTHROTTLE_TARGET_CONCURRENCY = 0.5
# AUTOTHROTTLE_DEBUG = True



TELNETCONSOLE_ENABLED = False
TELNETCONSOLE_PORT = None

configs = []
DOWNLOAD_FAIL_ON_DATALOSS = False

SPIDER_MIDDLEWARES = {
    # 'scrapy_splash.SplashDeduplicateArgsMiddleware': 100,
    # 'jobsitescraper.middlewares.jobsitescraperSpiderMiddleware': 900,
    # 'scrapy.contrib.spidermiddleware.depth.DepthMiddleware': None,
}


# DOWNLOADER_MIDDLEWARES = {
    #    'jobsitescraper.middlewares.jobsitescraperDownloaderMiddleware': 543,
    #    'scrapy_splash.SplashCookiesMiddleware': 723,
    #    'scrapy_splash.SplashMiddleware': 725,
    # 'jobsitescraper.middlewares.CustomProxyMiddleware': 350,
    # 'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware': 810,
    # 'rotating_proxies.middlewares.RotatingProxyMiddleware': 610,
    # 'rotating_proxies.middlewares.BanDetectionMiddleware': 620,
# }

DOWNLOADER_MIDDLEWARES = {
    'scrapy.downloadermiddlewares.useragent.UserAgentMiddleware': None,
    'scrapy_fake_useragent.middleware.RandomUserAgentMiddleware': 400,
    # 'scrapy_user_agents.middlewares.RandomUserAgentMiddleware': 400,

}

ITEM_PIPELINES = {
    #    'jobsitescraper.pipelines.jobsitescraperAssignImagePipeline': 100,
    #    'scrapy.pipelines.images.ImagesPipeline': 200,
    #    'jobsitescraper.pipelines.jobsitescraperUploadImagePipeline': 300,
    # 'jobsitescraper.pipelines.jobsitescraperPipeline': 400,
    #    'jobsitescraper.pipelines.jobsitescraperDeleteImagePipeline': 500,
}

IMAGES_STORE = './temp_images'
IMAGES_EXPIRES = 0
DOWNLOAD_TIMEOUT = 300

DOWNLOAD_HANDLERS = {
    "http": "scrapy_playwright.handler.ScrapyPlaywrightDownloadHandler",
    "https": "scrapy_playwright.handler.ScrapyPlaywrightDownloadHandler",
}

TWISTED_REACTOR = "twisted.internet.asyncioreactor.AsyncioSelectorReactor"

PLAYWRIGHT_BROWSER_TYPE = "chromium"

PLAYWRIGHT_LAUNCH_OPTIONS = {
    "headless": True,
}
#CLOSESPIDER_ITEMCOUNT = 50


# FEEDS = {
#     'jobsch.csv': {
#         'format': 'csv',
#         'overwrite': True,
#         'encoding': 'utf8',
#     },
# }
