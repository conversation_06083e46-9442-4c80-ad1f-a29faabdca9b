import os
import shutil
import traceback
from scrapy.crawler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from scrapy.utils.project import get_project_settings

from twisted.internet.task import LoopingCall
from twisted.internet import reactor, defer

from scrapy.utils.log import configure_logging
from jobsitescraper.config_manager import ConfigManager
import datetime
import timeit
import time
import numpy as np
import sys

if __name__ == "__main__":
    arg = sys.argv
    print(arg)
    sys.stdout.flush()
    if len(arg) > 1:

        # arg = bool(arg[1])
        arg = eval(arg[1])
    running_spiders = []
    chunk_numbers = 20
    if arg ==1 :
        arg = True
    else:
        arg = False
    def startProcess():
        configure_logging()
        
        runner = CrawlerRunner(get_project_settings())
        task = LoopingCall(lambda: def_process(runner))
        if arg == True:
            print("will run Today")
            sys.stdout.flush()
            task.start(30*60*1, True)
        else:
            print("will run Tomorrow")
            sys.stdout.flush()
            dt = datetime.datetime.now()
            tomorrow = dt + datetime.timedelta(days=1)
            s = (tomorrow-dt).total_seconds()
            # time.sleep(s)
            task.start(24*60*60*1, True)
        reactor.run()

    def def_process(runner):
        filter_list = []
        config_list = ConfigManager.getConfig(arg)
        remove_images()
        if len(config_list) > 0:
            for config in config_list:
                if config["IsActive"] is True and config["SourceKey"] in runner.spider_loader.list() \
                        and "Custom" in config and config["Custom"] is True:
                    filter_list.append(config)
            
            chunk_list = chunk(filter_list, chunk_numbers)
            
            for _c in chunk_list:
                jobs_ch= next(x for x in config_list if x["SourceKey"] == "jobs.ch" )
                _c = np.insert(_c, 0, jobs_ch, axis=0)
                run_chunk(runner, _c)

    @defer.inlineCallbacks
    def run_chunk(runner, config_chunk):
        for config in config_chunk:
            try:
                if config["SourceKey"] not in running_spiders:
                    running_spiders.append(config["SourceKey"])

                    yield runner.crawl(config["SourceKey"], _config=config)
                    running_spiders.remove(config["SourceKey"])
                else:
                    print(config["SourceKey"], "Skipped")
                    sys.stdout.flush()
            except:
                running_spiders.remove(config["SourceKey"])
                continue

    def chunk(it, size):
        return np.array_split(it, size)
    
    def remove_images():
        try:
            now = time.time()
            critical_time = now - 5 *60*1
            path = './temp_images/full'
            if os.path.exists(path):
                for f in os.listdir(path):
                    file_time =os.stat(os.path.join(path,f)).st_mtime
                    if  file_time < critical_time:
                        if os.path.isfile(os.path.join(path,f)):
                            os.remove(os.path.join(path, f))
                            print(f)
            print("Old imgages removed")
        except Exception as e:
            print(traceback.format_exc())
    startProcess()
